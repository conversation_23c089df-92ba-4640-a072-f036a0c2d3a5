import React, { useState, useEffect, useRef, createRef, useContext, useCallback } from 'react';
import { Search, Code, FileEdit, X, Loader2, ExternalLink, Clock, ChevronDown, ChevronUp, Eye, Play, RefreshCw } from 'lucide-react';
import { useSearchParams, usePathname, useRouter, useParams } from 'next/navigation';
import { useCodeGeneration } from '../Context/CodeGenerationContext';
import { listDeployments } from '@/utils/deploymentApi';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { useUser } from '../Context/UserContext';
import { resumeStartCodeGeneration, resumeStartCodeMaintenance } from '@/utils/api';
import { AlertContext } from '../NotificationAlertService/AlertList';

const TableSkeleton = () => (
  <>
    {[...Array(5)].map((_, idx) => (
      <tr key={idx} className="border-b border-gray-100 animate-pulse">
        {/* Type Column */}
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="flex items-center justify-center gap-3">
            <div className="p-2 bg-gray-100 rounded-lg">
              <div className="w-[18px] h-[18px] bg-gray-200 rounded"></div>
            </div>
            <div className="w-20 h-6 bg-gray-200 rounded-full"></div>
          </div>
        </td>

        {/* Session Column */}
        <td className="px-6 py-4 text-center">
          <div className="flex flex-col items-center">
            <div className="w-40 h-4 bg-gray-200 rounded mb-1"></div>
            <div className="w-24 h-3 bg-gray-100 rounded"></div>
          </div>
        </td>

        {/* Status Column */}
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="w-16 h-6 bg-gray-200 rounded-full mx-auto"></div>
        </td>

        {/* Date Column */}
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="w-20 h-4 bg-gray-200 rounded mb-1 mx-auto"></div>
          <div className="w-16 h-3 bg-gray-100 rounded mx-auto"></div>
        </td>

        {/* Duration Column */}
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="flex items-center justify-center">
            <div className="w-3.5 h-3.5 bg-gray-200 rounded mr-1.5"></div>
            <div className="w-8 h-4 bg-gray-200 rounded"></div>
          </div>
        </td>

        {/* Actions Column */}
        <td className="px-6 py-4 whitespace-nowrap text-center">
          <div className="flex items-center justify-center gap-2">
            <div className="w-[120px] h-8 bg-gray-200 rounded-lg"></div>
            <div className="w-[120px] h-8 bg-gray-200 rounded-lg"></div>
          </div>
        </td>
      </tr>
    ))}
  </>
);

const Sessions = ({ initialSessions = [], isLoading = false, onFilterChange, onCloseModal, onRefresh }) => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const { is_having_permission } = useUser();
  const { showAlert } = useContext(AlertContext);

  // State management
  const [sessions, setSessions] = useState(initialSessions);
  const [allSessions, setAllSessions] = useState(initialSessions);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('All types');
  const [statusFilter, setStatusFilter] = useState('All statuses');
  const [dateFilter, setDateFilter] = useState(null);
  const [sortField, setSortField] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const { isVisible, setIsVisible, setCurrentIframeUrl, setLlmModel } = useCodeGeneration();
  const [resumingSession, setResumingSession] = useState(null);
  const [processedRequests, setProcessedRequests] = useState(new Set());
  
  // Use ref for immediate blocking of duplicate requests
  const processingRequests = useRef(new Set());

  // Add debounce timer ref
  const debounceTimerRef = useRef(null);

  // Keep existing deployment logic
  const [activeDeploymentModal, setActiveDeploymentModal] = useState(null);
  const [sessionDeployments, setSessionDeployments] = useState({});
  const [loadingDeployments, setLoadingDeployments] = useState({});
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const deploymentRefs = useRef({});
  const { projectId } = useParams();

  // Fetch deployments for specific session
  const fetchSessionDeployments = async (sessionId) => {
    setLoadingDeployments(prev => ({ ...prev, [sessionId]: true }));
    try {
      const response = await listDeployments(projectId);
      const sortedDeployments = response.sort(
        (a, b) => new Date(b.created_at) - new Date(a.created_at)
      );
      setSessionDeployments(prev => ({
        ...prev,
        [sessionId]: sortedDeployments
      }));
    } catch (error) {
      showAlert("Failed to fetch deployments", "error");
    } finally {
      setLoadingDeployments(prev => ({ ...prev, [sessionId]: false }));
    }
  };

  // Handle showing deployments for specific session
  const handleShowDeployments = (sessionId, event) => {
    if (activeDeploymentModal === sessionId) {
      setActiveDeploymentModal(null);
    } else {
      const buttonRect = event.currentTarget.getBoundingClientRect();
      const viewportWidth = window.innerWidth;

      let top = buttonRect.bottom + window.scrollY + 5;
      let left = buttonRect.left + window.scrollX;

      if (left + 384 > viewportWidth) {
        left = Math.max(10, viewportWidth - 394);
      }

      setDropdownPosition({ top, left });
      setActiveDeploymentModal(sessionId);

      // Always fetch fresh deployments when opening the modal
        fetchSessionDeployments(sessionId);
    }
  };

  // Add click outside handler for deploy dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (activeDeploymentModal && !event.target.closest('.deployments-dropdown')) {
        setActiveDeploymentModal(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [activeDeploymentModal]);

  // Update sessions when props change
  useEffect(() => {
    setSessions(initialSessions);
    setAllSessions(initialSessions);
  }, [initialSessions]);

  // Auto-apply filters whenever filter values change
  useEffect(() => {
    applyFilters();
  }, [typeFilter, statusFilter, searchQuery, dateFilter, sortField, sortDirection]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Handle search input change with debouncing
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);
  };

  // Apply filters and sorting
  const applyFilters = useCallback(() => {
    const selectedType = typeFilter.trim().toLowerCase();
    const selectedStatus = statusFilter.trim().toLowerCase();
    const keyword = searchQuery.trim().toLowerCase();

    const selectedDate = dateFilter && dayjs(dateFilter).isValid()
      ? dayjs(dateFilter).format('YYYY-MM-DD')
      : null;

    let filtered = allSessions.filter((session) => {
      const sessionType = session.icon?.toLowerCase();
      const sessionStatus = session.status?.toLowerCase();
      const sessionTitle = (session.title || '').toLowerCase();

      const rawDate = session.date?.split('•')[0]?.trim();
      const parsedDate = dayjs(rawDate, ['MMM D, YYYY', 'MMMM D, YYYY'], true);
      const sessionDate = parsedDate.isValid() ? parsedDate.format('YYYY-MM-DD') : null;

      const matchesType =
        selectedType === 'all types' ||
        (selectedType === 'generation' && sessionType === 'code') ||
        (selectedType === 'maintenance' && sessionType === 'edit');

      const matchesStatus =
        selectedStatus === 'all statuses' || 
        sessionStatus === selectedStatus ||
        (selectedStatus === 'running' && (sessionStatus === 'running' || sessionStatus === 'in_progress' || sessionStatus === 'in progress')) ||
        (selectedStatus === 'completed' && (sessionStatus === 'complete' || sessionStatus === 'completed')) ||
        (selectedStatus === 'stopped' && sessionStatus === 'stopped');

      const matchesSearch = !keyword || sessionTitle.includes(keyword);

      const matchesDate = !selectedDate || sessionDate === selectedDate;

      return matchesType && matchesStatus && matchesSearch && matchesDate;
    });

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortField) {
        case 'title':
          aValue = a.title?.toLowerCase() || '';
          bValue = b.title?.toLowerCase() || '';
          break;
        case 'status':
          aValue = a.status?.toLowerCase() || '';
          bValue = b.status?.toLowerCase() || '';
          break;
        case 'type':
          aValue = a.icon?.toLowerCase() || '';
          bValue = b.icon?.toLowerCase() || '';
          break;
        case 'date':
        default:
          aValue = dayjs(a.date?.split('•')[0]?.trim(), ['MMM D, YYYY', 'MMMM D, YYYY']).valueOf() || 0;
          bValue = dayjs(b.date?.split('•')[0]?.trim(), ['MMM D, YYYY', 'MMMM D, YYYY']).valueOf() || 0;
          break;
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setSessions(filtered);
  }, [allSessions, typeFilter, statusFilter, searchQuery, dateFilter, sortField, sortDirection]);

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get icon based on type
  const getIcon = (iconType) => {
    switch (iconType) {
      case 'code':
        return <Code size={18} className="text-green-600" />;
      case 'edit':
        return <FileEdit size={18} className="text-purple-600" />;
      default:
        return <Code size={18} className="text-gray-600" />;
    }
  };

  // Get type badge style
  const getTypeBadgeStyle = (iconType) => {
    switch (iconType) {
      case 'code':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'edit':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  const handleRowClick = (row) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("task_id", row.id);
    router.push(`${pathname}?${newSearchParams.toString()}`);
    
    // Note: The CodeGenerationContext will automatically open the modal
    // when it detects the task_id in the URL - no need to call setIsVisible
  };

  // Get type label
  const getTypeLabel = (iconType) => {
    switch (iconType) {
      case 'code':
        return 'Generation';
      case 'edit':
        return 'Maintenance';
      default:
        return 'Unknown';
    }
  };

  // Get status style
  const getStatusStyle = (status) => {
    switch (status?.toLowerCase()) {
      case 'running':
      case 'in_progress':
      case 'in progress':
        return 'bg-orange-50 text-orange-700 border-orange-200';
      case 'complete':
      case 'completed':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'submitted':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'stopped':
        return 'bg-gray-50 text-gray-700 border-gray-200';
      case 'failed':
        return 'bg-red-50 text-red-700 border-red-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  // Format status display with proper capitalization
  const formatStatusDisplay = (status) => {
    if (!status) return 'Unknown';
    
    const statusLower = status.toLowerCase();
    
    // Handle special cases
    if (statusLower === 'in_progress' || statusLower === 'in progress' || statusLower === 'running') {
      return 'Running';
    }
    
    if (statusLower === 'complete' || statusLower === 'completed') {
      return 'Completed';
    }
    
    if (statusLower === 'stopped') {
      return 'Stopped';
    }
    
    if (statusLower === 'submitted') {
      return 'Submitted';
    }
    
    if (statusLower === 'failed') {
      return 'Failed';
    }
    
    // Capitalize first letter for all other statuses
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  // Handle resume functionality
  const handleResumeClick = async (session) => {
    if (!session.id) {
      showAlert("Session ID not found", "error");
      return;
    }

    // Immediate blocking using ref to prevent race conditions
    if (processingRequests.current.has(session.id)) {
      
      return;
    }

    // Also check state-based tracking
    if (processedRequests.has(session.id)) {
      
      return;
    }

    // Add to both immediate ref and state tracking
    processingRequests.current.add(session.id);
    setProcessedRequests(prev => new Set([...prev, session.id]));
    setResumingSession(session.id);

    try {
      let response;
      
      if (session.icon === 'edit') {
        const selectedRepos = { all_repositories: true };
        const sessionName = "Untitled";
        const sessionDescription = "Resumed maintenance session";
        
        try {
          response = await resumeStartCodeMaintenance(
            projectId,
            session.id,
            selectedRepos,
            sessionName,
            sessionDescription
          );
        } catch (resumeError) {
          console.error('Resume API error details:', {
            message: resumeError.message,
            status: resumeError.status,
            response: resumeError.response,
            stack: resumeError.stack
          });
          
          // Handle specific duplicate request error
          if (resumeError.message && resumeError.message.includes("already finished loading")) {
            
            showAlert("This session may have been resumed already. Please check your active sessions.", "warning");
            return;
          }
          
          throw resumeError;
        }
        
        // Check if response indicates a duplicate request
        if (response && response.duplicate) {
          
          showAlert(response.message || "This session may have been resumed already. Please check your active sessions.", "warning");
          return;
        }
      } else {
        const architectureId = session.architecture_id || null;
        let containerIds = [];
        
        if (session.container_ids && Array.isArray(session.container_ids)) {
          const flattenDeep = (arr) => {
            return arr.reduce((acc, val) => {
              if (Array.isArray(val)) {
                return acc.concat(flattenDeep(val));
              } else {
                const num = parseInt(val);
                if (!isNaN(num)) {
                  acc.push(num);
                }
                return acc;
              }
            }, []);
          };
          
          containerIds = flattenDeep(session.container_ids);
        } else if (session.container_id) {
          const containerId = parseInt(session.container_id);
          if (!isNaN(containerId)) {
            containerIds = [containerId];
          }
        }

        try {
          response = await resumeStartCodeGeneration(
            projectId,
            architectureId,
            session.id,
            containerIds
          );
        } catch (resumeError) {
          // Handle specific duplicate request error for code generation too
          if (resumeError.message && resumeError.message.includes("already finished loading")) {
            
            showAlert("This session may have been resumed already. Please check your active sessions.", "warning");
            return;
          }
          
          throw resumeError;
        }
      }

      if (response && response.task_id) {
        console.log("Sessions: Got task_id:", response.task_id);
        console.log("Sessions: Response end flag:", response.end);

        // Close modal first if it's open
        // if (onCloseModal) {
        //   onCloseModal();
        // }

        // Navigate to the task_id URL
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("task_id", response.task_id);
        const newUrl = `${pathname}?${newSearchParams.toString()}`;
        
        console.log("Sessions: Redirecting to:", newUrl);
        router.push(newUrl);
        
        // Note: The CodeGenerationContext will automatically open the modal
        // when it detects the task_id in the URL - no need to call setIsVisible
        console.log("Sessions: Modal will open automatically via context when URL updates");

      } else if (response && response.error) {
        // Handle API-level duplicate error messages
        if (response.duplicate || response.error.includes("already finished loading")) {
          showAlert(response.message || "This session may have been resumed already. Please check your active sessions.", "warning");
        } else {
          showAlert(response.error, "error");
        }
      } else if (response && response.message && !response.task_id) {
        // Only handle messages that don't have task_id (to avoid conflicts)
        console.log("Sessions: Got message without task_id:", response.message);
        showAlert(response.message, response.end ? "success" : "info");
        if (response.end && onCloseModal) {
          onCloseModal();
        }
      } else {
        console.log("Sessions: Unexpected response format:", response);
        showAlert("Unexpected response format. Please try again.", "warning");
      }
    } catch (error) {
      console.error(`Failed to resume ${session.icon === 'edit' ? 'code maintenance' : 'code generation'}:`, error);

      let errorMessage = `Failed to resume ${session.icon === 'edit' ? 'code maintenance' : 'code generation'}. Please try again.`;

      // Handle specific duplicate request errors
      if (error.message && error.message.includes("already finished loading")) {
        errorMessage = "This session may have been resumed already. Please check your active sessions.";
        showAlert(errorMessage, "warning");
        return;
      } else if (error.message && error.message.includes("JSON")) {
        errorMessage = "Response format error. Please try again or contact support.";
      } else if (error.message && (error.message.includes("network") || error.message.includes("fetch"))) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      showAlert(errorMessage, "error");
    } finally {
      setResumingSession(null);
      
      // Clean up both ref and state tracking
      processingRequests.current.delete(session.id);
      setProcessedRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(session.id);
        return newSet;
      });
    }
  };

  // Add refresh handler
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      if (onRefresh) {
        await onRefresh();
      }
    } catch (error) {
      console.error('Error refreshing sessions:', error);
      showAlert("Failed to refresh sessions", "error");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Render deployment button
  const renderDeploymentButton = (session) => {
    const sessionDeploys = sessionDeployments[session.id] || [];
    const isLoadingDep = loadingDeployments[session.id];
    const deploymentCount = sessionDeploys.length;

    if (!deploymentRefs.current[session.id]) {
      deploymentRefs.current[session.id] = createRef();
    }

    return (
      <div className="relative deployments-dropdown" ref={deploymentRefs.current[session.id]}>
        <button
          onClick={(e) => handleShowDeployments(session.id, e)}
          data-session-id={session.id}
          className="inline-flex items-center justify-center gap-1.5 px-3 py-1.5 text-sm font-medium bg-orange-500 text-white hover:bg-orange-600 rounded-lg shadow-sm transition-colors duration-200 min-w-[120px]"
        >
          <ExternalLink size={14} />
          <span>Deployed Apps</span>
          {deploymentCount > 0 && (
            <span className="flex items-center justify-center bg-white text-orange-600 rounded-full h-5 w-5 text-xs font-semibold">
              {deploymentCount}
            </span>
          )}
        </button>

        {activeDeploymentModal === session.id && (
          <div
            className="fixed w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-[100] max-h-[80vh] flex flex-col"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              maxHeight: "80vh"
            }}
          >
            <div className="p-4 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <span className="w-2 h-2 rounded-full bg-orange-500"></span>
                  Deployment History
                </h3>
                <div className="flex items-center gap-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      fetchSessionDeployments(session.id);
                    }}
                    className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                    title="Refresh deployments"
                  >
                    <RefreshCw size={16} className={`${loadingDeployments[session.id] ? 'animate-spin' : ''}`} />
                  </button>
                <button
                  onClick={() => setActiveDeploymentModal(null)}
                  className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
                >
                  <X size={16} />
                </button>
                </div>
              </div>
              <p className="typography-caption text-gray-500 mt-1">{session.title}</p>
            </div>

            <div className="overflow-y-auto p-4 flex-grow">
              {isLoadingDep ? (
                <div className="flex flex-col justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-orange-500 mb-2" />
                  <p className="text-sm text-gray-500">Loading deployments...</p>
                </div>
              ) : sessionDeploys.length === 0 ? (
                <div className="text-center py-8 px-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <ExternalLink className="h-8 w-8 text-gray-400" />
                  </div>
                  <p className="text-base font-medium text-gray-700">No deployments found</p>
                  <p className="text-sm text-gray-500 mt-1">There are no deployments for this session yet.</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {sessionDeploys.map((deployment) => (
                    <div
                      key={deployment.id}
                      className="bg-white rounded-md p-3 border border-gray-200 hover:border-orange-200 transition-all duration-200 cursor-pointer"
                      onClick={() => {
                        if (deployment.custom_domain || deployment.app_url) {
                          window.open(deployment.custom_domain || deployment.app_url, '_blank');
                        }
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate text-left">
                            {deployment.root_path
                              ? deployment.root_path.split("/").filter(Boolean).pop()
                              : "Unknown Folder"}
                          </p>
                          <div className="flex items-center gap-2 mt-1">
                            <p className="text-xs text-gray-500 flex items-center gap-1 text-left">
                              <span className="inline-block w-2 h-2 rounded-full bg-gray-300"></span>
                              <span className="truncate">{deployment.branch_name || "Default Branch"}</span>
                            </p>
                            {(deployment.custom_domain || deployment.app_url) && (
                              <a
                                href={deployment.custom_domain || deployment.app_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 ml-2 flex-shrink-0"
                                onClick={(e) => e.stopPropagation()}
                                title={deployment.custom_domain || deployment.app_url}
                              >
                                <ExternalLink size={12} />
                              </a>
                            )}
                          </div>
                        </div>
                        <span className={`flex-shrink-0 px-2 py-1 text-xs font-medium rounded-full whitespace-nowrap border ${
                          deployment.status === "success" && deployment.domain_status
                            ? deployment.domain_status === "available"
                              ? "bg-green-50 text-green-700 border-green-200"
                              : deployment.domain_status === "inprogress"
                              ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                              : "bg-gray-50 text-gray-700 border-gray-200"
                            : deployment.status === "success"
                            ? "bg-green-50 text-green-700 border-green-200"
                            : deployment.status === "processing"
                            ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                            : "bg-red-50 text-red-700 border-red-200"
                          }`}>
                          {deployment.status === "success" && deployment.domain_status === "inprogress"
                            ? "processing"
                            : deployment.status}
                        </span>
                      </div>

                      {/* Add domain status messages */}
                      {deployment.status === "success" && deployment.domain_status === "inprogress" && (
                        <div className="flex items-center gap-2 mt-2">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                          <p className="text-xs text-yellow-700 font-medium text-left">
                            Domain is processing
                          </p>
                        </div>
                      )}
                      {deployment.status === "success" && (deployment.domain_status === "success" || deployment.domain_status === "available") && (
                        <div className="flex items-center gap-2 mt-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                          <p className="text-xs text-green-700 font-medium text-left">
                            Your app is live now
                          </p>
                        </div>
                      )}
                      <div className="mt-2 text-xs text-gray-500 text-left">
                        {new Date(deployment.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render table header with sorting
  const renderTableHeader = (field, label) => (
    <th 
      className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider cursor-pointer hover:bg-gray-50 transition-colors duration-200"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center justify-center gap-2">
        <span>{label}</span>
        {sortField === field && (
          sortDirection === 'asc' ? 
            <ChevronUp size={14} className="text-gray-400" /> : 
            <ChevronDown size={14} className="text-gray-400" />
        )}
      </div>
    </th>
  );

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header and Filters */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-8 py-6">
          {/* Title Section */}
          <div className="mb-8 flex items-start justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Filter Sessions</h1>
              <div className="w-16 h-1 bg-red-500 rounded-full"></div>
            </div>
            <button
              onClick={onCloseModal}
              className="p-1.5 rounded-md hover:bg-gray-100 transition-colors"
              aria-label="Close modal"
            >
              <X className="w-5 h-5 text-gray-500 hover:text-gray-700" />
            </button>
          </div>

          {/* Filter Section */}
          <div className="rounded-lg p-4 border border-gray-100 max-w-full">
            <div className="flex items-center gap-3">
              
              {/* Search */}
              <div className="relative w-[280px]">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search by session name or ID..."
                  className="block w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white transition-all duration-200 hover:border-gray-400"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>

              {/* Type Filter */}
              <div className="relative w-[130px]">
                <select
                  className="w-full pl-4 pr-10 py-2.5 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white appearance-none transition-all duration-200 hover:border-gray-400"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <option>All types</option>
                  <option>Generation</option>
                  <option>Maintenance</option>
                </select>
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </div>
              </div>

              {/* Status Filter */}
              <div className="relative w-[130px]">
                <select
                  className="w-full pl-4 pr-10 py-2.5 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white appearance-none transition-all duration-200 hover:border-gray-400"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option>All statuses</option>
                  <option>Running</option>
                  <option>Completed</option>
                  <option>Submitted</option>
                  <option>Stopped</option>
                  <option>Failed</option>
                </select>
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </div>
              </div>

              {/* Date Filter */}
              <div className="w-[180px]">
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    value={dateFilter}
                    onChange={(newValue) => {
                      setDateFilter(newValue);
                    }}
                    format="MM/DD/YYYY"
                    slotProps={{
                      textField: {
                        size: "small",
                        className: "w-full border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500",
                        placeholder: "MM/DD/YYYY"
                      },
                      field: {
                        clearable: true,
                        onClear: () => {
                          setDateFilter(null);
                        }
                      }
                    }}
                  />
                </LocalizationProvider>
              </div>

              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className={`flex items-center justify-center gap-1.5 px-3 py-2.5 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 whitespace-nowrap ${isRefreshing ? 'cursor-not-allowed opacity-50' : ''}`}
                aria-label="Refresh sessions"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                <span className="text-sm font-medium">Refresh</span>
              </button>

            </div>

            {/* Active Filters - Compact Display */}
            {(searchQuery || typeFilter !== 'All types' || statusFilter !== 'All statuses' || dateFilter) && (
              <div className="mt-3 flex flex-wrap gap-1.5">
                {searchQuery && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-orange-50 text-orange-700 border border-orange-200 rounded text-xs">
                    "{searchQuery}"
                    <button
                      onClick={() => setSearchQuery('')}
                      className="hover:bg-orange-100 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
                
                {typeFilter !== 'All types' && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 border border-blue-200 rounded text-xs">
                    {typeFilter}
                    <button
                      onClick={() => setTypeFilter('All types')}
                      className="hover:bg-blue-100 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
                
                {statusFilter !== 'All statuses' && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-50 text-green-700 border border-green-200 rounded text-xs">
                    {statusFilter}
                    <button
                      onClick={() => setStatusFilter('All statuses')}
                      className="hover:bg-green-100 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
                
                {dateFilter && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-purple-50 text-purple-700 border border-purple-200 rounded text-xs">
                    {dayjs(dateFilter).format('MMM D, YYYY')}
                    <button
                      onClick={() => setDateFilter(null)}
                      className="hover:bg-purple-100 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-auto">
          {isLoading ? (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Session</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Duration</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <TableSkeleton />
              </tbody>
            </table>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  {renderTableHeader('type', 'Type')}
                  {renderTableHeader('title', 'Session')}
                  {renderTableHeader('status', 'Status')}
                  {renderTableHeader('date', 'Date')}
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Duration</th>
                  <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sessions.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="px-6 py-16 text-center">
                      <div className="flex flex-col items-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                          <Search className="h-8 w-8 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-1">No sessions found</h3>
                        <p className="text-sm text-gray-500 max-w-md">
                          We couldn't find any sessions matching your current filters. Try adjusting your search criteria or create a new session.
                        </p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  sessions.map((session) => {
                    const isRunningOrInProgress = session.status?.toLowerCase() === 'running' || session.status?.toLowerCase() === 'in_progress' || session.status?.toLowerCase() === 'in progress';
                    const isViewMode = isRunningOrInProgress;
                    const isResumeDisabled = !isViewMode && (resumingSession === session.id || processedRequests.has(session.id) || (session.status?.toLowerCase() !== 'complete' && session.status?.toLowerCase() !== 'completed' && session.status?.toLowerCase() !== 'failed' && session.status?.toLowerCase() !== 'submitted' && session.status?.toLowerCase() !== 'stopped'));

                    return (
                      <tr key={session.id} className="hover:bg-gray-50 transition-colors duration-200">
                        {/* Type */}
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="flex items-center justify-center gap-3">
                            <div className="p-2 bg-gray-50 rounded-lg">
                              {getIcon(session.icon)}
                            </div>
                            <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getTypeBadgeStyle(session.icon)}`}>
                              {getTypeLabel(session.icon)}
                            </span>
                          </div>
                        </td>

                        {/* Session Title */}
                        <td className="px-6 py-4 text-center">
                          <div className="flex flex-col items-center">
                            <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                              {session.title}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              ID: {session.id}
                            </div>
                          </div>
                        </td>

                        {/* Status */}
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusStyle(session.status)}`}>
                            {(session.status?.toLowerCase() === 'running' || session.status?.toLowerCase() === 'in_progress') && <div className="w-2 h-2 bg-orange-500 rounded-full mr-1.5 animate-pulse"></div>}
                            {formatStatusDisplay(session.status)}
                          </span>
                        </td>

                        {/* Date */}
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="text-sm text-gray-900">
                            {session.date?.split('•')[0]?.trim()}
                          </div>
                          <div className="text-xs text-gray-500">
                            {session.date?.split('•')[1]?.trim()}
                          </div>
                        </td>

                        {/* Duration */}
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="flex items-center justify-center text-sm text-gray-600">
                            <Clock size={14} className="mr-1.5" />
                            {session.duration}
                          </div>
                        </td>

                        {/* Actions */}
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="flex items-center justify-center gap-2">
                            <button
                              onClick={() => isViewMode ? handleRowClick(session) : handleResumeClick(session)}
                              disabled={!is_having_permission() || (!isViewMode && isResumeDisabled)}
                              className={`inline-flex items-center justify-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200 min-w-[120px] ${
                                (!is_having_permission() || (!isViewMode && isResumeDisabled))
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                  : 'bg-orange-50 text-orange-700 hover:bg-orange-100 border border-orange-200'
                              }`}
                            >
                              {!isViewMode && resumingSession === session.id ? (
                                <>
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span>Resuming...</span>
                                </>
                              ) : (
                                <>
                                  {isViewMode ? <Eye size={14} /> : <Play size={14} />}
                                  <span>{isViewMode ? 'View' : 'Resume'}</span>
                                </>
                              )}
                            </button>
                            {renderDeploymentButton(session)}
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sessions;