import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Info, XCircle, X } from 'lucide-react';
import { cn } from "@/lib/utils";

const AlertBox = ({
  title = "Notification",
  content,
  type = 'default',
  onClose,
  duration = 3000
}) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (!duration) return;

    const timer = setTimeout(() => {
      setVisible(false);
      onClose?.();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  if (!visible || !content) return null;

  const variants = {
    success: {
      icon: CheckCircle,
      iconColor: "text-green-600 dark:text-green-400"
    },
    danger: {
      icon: XCircle,
      iconColor: "text-red-600 dark:text-red-400"
    },
    info: {
      icon: Info,
      iconColor: "text-primary-600 dark:text-primary-400"
    },
    warning: {
      icon: AlertCircle,
      iconColor: "text-yellow-600 dark:text-yellow-400"
    },
    error: {
      icon: XCircle,
      iconColor: "text-red-600 dark:text-red-400"
    },
    default: {
      icon: Info,
      iconColor: "text-primary-600 dark:text-primary-400"
    }
  };

  const variant = variants[type] || variants.default;
  const Icon = variant.icon;

  return (
    <div
      role="alert"
      className={cn(
        "fixed top-6 right-6 max-w-sm min-w-80 p-3 rounded-lg border",
        "flash-alert shadow-lg hover:shadow-xl transition-all duration-200",
        `flash-alert-${type}`
      )}
      style={{ zIndex: 9999 }}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          <Icon className={cn("h-4 w-4", variant.iconColor)} />
        </div>

        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-custom-text-primary mb-1">
            {title}
          </div>
          {content && (
            <div className="text-xs text-custom-text-secondary leading-relaxed">
              {content}
            </div>
          )}
        </div>

        <button
          onClick={onClose}
          className={cn(
            "flex-shrink-0 rounded-md p-1.5 text-custom-text-secondary",
            "hover:text-custom-text-primary hover:bg-custom-bg-secondary",
            "transition-all duration-150 ease-in-out",
            "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1"
          )}
          aria-label="Close notification"
        >
          <X className="h-3.5 w-3.5" />
        </button>
      </div>
    </div>
  );
};

export default AlertBox;
